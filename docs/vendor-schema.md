# Vendor Database Schema

## Vendors Collection

The `vendors` collection stores information about Title Companies, Lenders, and other vendor types.

### Vendor Document Structure

```javascript
{
  id: "vendor_id", // Auto-generated document ID
  companyName: "ABC Title Company",
  vendorType: "titleCompany", // "titleCompany", "lender", "vendor"
  subscriptionLevel: "premier", // "premier", "standard"
  logoUrl: "https://storage.googleapis.com/...", // Company logo URL
  contactEmail: "<EMAIL>",
  contactPhone: "(*************",
  website: "https://www.abctitle.com",
  customRoleTitles: [
    "Closer",
    "Assistant", 
    "Sales Person",
    "Marketing Lead",
    "Manager",
    "Processor",
    "Coordinator"
  ],
  createdAt: timestamp,
  updatedAt: timestamp
}
```

### Offices Subcollection

Path: `vendors/{vendorId}/offices/{officeId}`

```javascript
{
  id: "office_id", // Auto-generated document ID
  vendorId: "vendor_id", // Reference to parent vendor
  officeName: "Downtown Office",
  address: {
    street: "123 Main St",
    unit: "Suite 100",
    city: "Denver",
    state: "CO",
    zipcode: "80202"
  },
  phone: "(*************",
  fax: "(*************",
  managerName: "John Manager",
  managerPhone: "(*************",
  managerEmail: "<EMAIL>",
  createdAt: timestamp,
  updatedAt: timestamp
}
```

### People Subcollection

Path: `vendors/{vendorId}/people/{personId}`

```javascript
{
  id: "person_id", // Auto-generated document ID
  vendorId: "vendor_id", // Reference to parent vendor
  officeId: "office_id", // Reference to office
  firstName: "Jane",
  middleName: "M",
  lastName: "Closer",
  roleTitle: "Senior Closer",
  phone: "(*************",
  fax: "(*************",
  email: "<EMAIL>",
  assistantName: "Assistant Name",
  assistantPhone: "(*************",
  assistantEmail: "<EMAIL>",
  secondAssistantName: "Second Assistant",
  secondAssistantPhone: "(*************",
  secondAssistantEmail: "<EMAIL>",
  createdAt: timestamp,
  updatedAt: timestamp
}
```

## User Profile Updates for Title Admin

For users with role="titleadmin" and type="user", add these fields:

```javascript
{
  // ... existing user fields
  role: "titleadmin",
  type: "user",
  vendorId: "vendor_id", // Reference to their vendor company
  companyName: "ABC Title Company",
  companyLogoUrl: "https://storage.googleapis.com/...",
  // ... other fields
}
```

## Order Preferences Storage

For saving user preferences for future orders, add to user profile:

```javascript
{
  // ... existing user fields
  orderPreferences: {
    titleCompany: {
      companyId: "vendor_id",
      officeId: "office_id", 
      personId: "person_id"
    }
  }
}
```
