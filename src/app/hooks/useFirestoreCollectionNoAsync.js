import { useEffect } from "react";
import { dataFromSnapshot } from "../firestore/firestoreService";
import { onSnapshot } from "@firebase/firestore";

export default function useFirestoreCollectionNoAsync({
  query,
  data,
  deps,
  retainState,
}) {
  useEffect(() => {
    console.log("Running");
    if (retainState) {
      return;
    }

    const queryResult = query();

    // If query returns null, don't attempt to create a subscription
    if (!queryResult) {
      console.log("Query returned null, skipping subscription");
      return;
    }

    console.log("No data fetch");
    const unsubscribe = onSnapshot(
      queryResult,
      (snapshot) => {
        const docs = snapshot.docs.map((doc) => dataFromSnapshot(doc));
        data(docs);
      },
      (error) => {
        // Handle permission-denied errors gracefully for non-user agents
        if (error.code === 'permission-denied') {
          console.warn('Firestore permission denied - this is expected for non-user agents:', error.message);
        } else {
          console.log(error);
        }
      }
    );
    return () => {
      unsubscribe();
    };
  }, deps); // eslint-disable-line react-hooks/exhaustive-deps
}
