import { useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  asyncActionError,
  asyncActionFinish,
  asyncActionStart,
} from "../async/asyncSlice";
import { dataFromSnapshot } from "../firestore/firestoreService";
import { onSnapshot } from "@firebase/firestore";

export default function useFirestoreCollection({
  query,
  data,
  deps,
  retainState,
}) {
  const dispatch = useDispatch();

  useEffect(() => {
    const queryResult = query();

    // If query returns null, don't attempt to create a subscription
    if (!queryResult) {
      dispatch(asyncActionFinish());
      return;
    }

    dispatch(asyncActionStart());
    const unsubscribe = onSnapshot(
      queryResult,
      (snapshot) => {
        const docs = snapshot.docs.map((doc) => dataFromSnapshot(doc));
        data(docs);
        dispatch(asyncActionFinish());
      },
      (error) => {
        // Handle permission-denied errors gracefully for non-user agents
        if (error.code === 'permission-denied') {
          console.warn('Firestore permission denied - this is expected for non-user agents:', error.message);
          // Don't dispatch error for permission-denied, just finish the async action
          dispatch(asyncActionFinish());
        } else {
          // For other errors, dispatch the error as usual
          dispatch(asyncActionError(error));
        }
      }
    );
    return () => {
      unsubscribe();
    };
  }, deps); // eslint-disable-line react-hooks/exhaustive-deps
}
