export function coListingAddendumSellerDC120() {
return [   //2024 Release 2025-06-02 16:02:16
 {
   page: 0,
 name: "<PERSON><PERSON>",
 type: "logo",
 top: 63.673,
 left: 53,
 width: 180,
 height: 27,
 }, 
{
   page: 0,
   name: "undefined", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 51,
   left: 260.04,
   width: 268.44,
   height: 10.2
}
,
{
   page: 0,
   name: "without right of transfer All rights reserved", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 744.36,
   left: 108.96,
   width: 228.60,
   height: 10.20
}
,
{
   page: 0,
   name: "Text8", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 156.128,
   left: 215.346,
   width: 325.419,
   height: 13.491
}
,
{
   page: 0,
   name: "Text9", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 178.037,
   left: 215.346,
   width: 325.419,
   height: 13.491
}
,
{
   page: 0,
   name: "Text10", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 198.983,
   left: 215.346,
   width: 325.419,
   height: 13.491
}
,
{
   page: 0,
   name: "Text11", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 220.928,
   left: 215.346,
   width: 325.419,
   height: 13.491
}
,
{
   page: 0,
   name: "Text12", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 241.874,
   left: 215.346,
   width: 325.419,
   height: 13.491
}
,
{
   page: 0,
   name: "Text13", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 263.783,
   left: 215.346,
   width: 325.419,
   height: 13.491
}
,
{
   page: 0,
   name: "Text14", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 284.729,
   left: 215.346,
   width: 325.419,
   height: 13.490
}
,
{
   page: 0,
   name: "Text15", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 541.93,
   left: 171.492,
   width: 208.909,
   height: 12.181
}
,
{
   page: 0,
   name: "Text16", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 587.439,
   left: 92.2913,
   width: 436.0377,
   height: 12.836
}
,
{
   page: 1,
   name: "undefined_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 51,
   left: 260.04,
   width: 268.44,
   height: 10.2
}
,
{
   page: 1,
   name: "Other", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 505.6,
   left: 131.48,
   width: 310.342,
   height: 11.531
}
,
{
   page: 1,
   name: "Other_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 519.52,
   left: 131.48,
   width: 310.342,
   height: 11.531
}
,
{
   page: 1,
   name: "Other_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 533.44,
   left: 131.48,
   width: 310.342,
   height: 11.531
}
,
{
   page: 1,
   name: "without right of transfer All rights reserved_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 744.36,
   left: 108.96,
   width: 228.60,
   height: 10.20
}
,
{
   page: 1,
   name: "Check Box17", 
   isText: false,
   type: "checkbox",
   top: 159.437,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box18", 
   isText: false,
   type: "checkbox",
   top: 157.782,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box19", 
   isText: false,
   type: "checkbox",
   top: 193.474,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box20", 
   isText: false,
   type: "checkbox",
   top: 192.783,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box21", 
   isText: false,
   type: "checkbox",
   top: 213.11,
   left: 463.457,
   width: 11.800,
   height: 11.80
}
,
{
   page: 1,
   name: "Check Box22", 
   isText: false,
   type: "checkbox",
   top: 213.074,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box23", 
   isText: false,
   type: "checkbox",
   top: 228.165,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box24", 
   isText: false,
   type: "checkbox",
   top: 227.474,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box25", 
   isText: false,
   type: "checkbox",
   top: 241.91,
   left: 463.457,
   width: 11.800,
   height: 11.80
}
,
{
   page: 1,
   name: "Check Box26", 
   isText: false,
   type: "checkbox",
   top: 240.565,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box27", 
   isText: false,
   type: "checkbox",
   top: 253.692,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box28", 
   isText: false,
   type: "checkbox",
   top: 254.31,
   left: 516.784,
   width: 11.800,
   height: 11.80
}
,
{
   page: 1,
   name: "Check Box29", 
   isText: false,
   type: "checkbox",
   top: 269.401,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box30", 
   isText: false,
   type: "checkbox",
   top: 266.092,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box31", 
   isText: false,
   type: "checkbox",
   top: 282.492,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box32", 
   isText: false,
   type: "checkbox",
   top: 281.147,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box33", 
   isText: false,
   type: "checkbox",
   top: 297.547,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box34", 
   isText: false,
   type: "checkbox",
   top: 294.892,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box35", 
   isText: false,
   type: "checkbox",
   top: 311.292,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box36", 
   isText: false,
   type: "checkbox",
   top: 309.638,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box37", 
   isText: false,
   type: "checkbox",
   top: 323.074,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box38", 
   isText: false,
   type: "checkbox",
   top: 323.42,
   left: 516.784,
   width: 11.800,
   height: 11.80
}
,
{
   page: 1,
   name: "Check Box39", 
   isText: false,
   type: "checkbox",
   top: 339.438,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box40", 
   isText: false,
   type: "checkbox",
   top: 337.438,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box41", 
   isText: false,
   type: "checkbox",
   top: 353.838,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box42", 
   isText: false,
   type: "checkbox",
   top: 351.183,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box43", 
   isText: false,
   type: "checkbox",
   top: 366.929,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box44", 
   isText: false,
   type: "checkbox",
   top: 365.583,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box45", 
   isText: false,
   type: "checkbox",
   top: 387.22,
   left: 463.457,
   width: 11.800,
   height: 11.80
}
,
{
   page: 1,
   name: "Check Box46", 
   isText: false,
   type: "checkbox",
   top: 386.529,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box47", 
   isText: false,
   type: "checkbox",
   top: 408.165,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box48", 
   isText: false,
   type: "checkbox",
   top: 407.511,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box49", 
   isText: false,
   type: "checkbox",
   top: 429.766,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box50", 
   isText: false,
   type: "checkbox",
   top: 428.456,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box51", 
   isText: false,
   type: "checkbox",
   top: 447.438,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box52", 
   isText: false,
   type: "checkbox",
   top: 448.747,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box53", 
   isText: false,
   type: "checkbox",
   top: 463.147,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box54", 
   isText: false,
   type: "checkbox",
   top: 461.802,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box55", 
   isText: false,
   type: "checkbox",
   top: 478.202,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box56", 
   isText: false,
   type: "checkbox",
   top: 475.548,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box57", 
   isText: false,
   type: "checkbox",
   top: 490.02,
   left: 463.457,
   width: 11.800,
   height: 11.80
}
,
{
   page: 1,
   name: "Check Box58", 
   isText: false,
   type: "checkbox",
   top: 488.984,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box59", 
   isText: false,
   type: "checkbox",
   top: 504.42,
   left: 463.457,
   width: 11.800,
   height: 11.80
}
,
{
   page: 1,
   name: "Check Box60", 
   isText: false,
   type: "checkbox",
   top: 503.42,
   left: 516.784,
   width: 11.800,
   height: 11.80
}
,
{
   page: 1,
   name: "Check Box61", 
   isText: false,
   type: "checkbox",
   top: 519.475,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box62", 
   isText: false,
   type: "checkbox",
   top: 518.475,
   left: 516.784,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box63", 
   isText: false,
   type: "checkbox",
   top: 532.566,
   left: 463.457,
   width: 11.800,
   height: 11.800
}
,
{
   page: 1,
   name: "Check Box64", 
   isText: false,
   type: "checkbox",
   top: 533.53,
   left: 516.784,
   width: 11.800,
   height: 11.80
}
,
{
   page: 2,
   name: "agree to indemnify and hold harmless the other Broker and Brokerage Firm if not found liable", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 51.338,
   left: 260.04,
   width: 268.44,
   height: 9.862
}
,
{
   page: 2,
   name: "Commission Distribution at Closing The compensation to the Brokerage Firm set forth in the Seller", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 112.015,
   left: 90.0,
   width: 467.991,
   height: 52.407
}
,
{
   page: 2,
   name: "AGREED", 
   type: "textarea",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 197.171,
   left: 90.6545,
   width: 457.5185,
   height: 53.062
}
,
{
   page: 2,
   name: "Seller", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 307.055,
   left: 100.8,
   width: 163.102,
   height: 12.480
}
,
{
   page: 2,
   name: "signature Broker 1", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 345.52,
   left: 72.0,
   width: 216.12,
   height: 12.48
}
,
{
   page: 2,
   name: "Seller_2", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 307.055,
   left: 352.8,
   width: 163.102,
   height: 12.480
}
,
{
   page: 2,
   name: "signature Broker 2", 
   type: "signature",
   fontName: "Times New Roman",
   fontSize: 9,
   top: 345.52,
   left: 324.0,
   width: 216.12,
   height: 12.48
}
,
{
   page: 2,
   name: "without right of transfer All rights reserved_3", 
   type: "text",
   fontName: "Times New Roman",
   fontSize: 8,
   top: 744.36,
   left: 108.96,
   width: 228.60,
   height: 10.20
}
] }
