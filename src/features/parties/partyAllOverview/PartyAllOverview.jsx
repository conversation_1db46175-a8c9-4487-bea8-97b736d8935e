import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Grid, Table, Message, Header } from "semantic-ui-react";
import PartyAllListItem from "./PartyAllListItem";
import LoadingComponent from "../../../app/layout/LoadingComponent";
import _ from "lodash";
import { sortPartiesForUser } from "../partySlice";

export default function PartyAllOverview() {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);

  const { partiesForUser, transactionsForUser, docsNeedingSignature } =
    useSelector((state) => state.party);
  const [loaded, setLoaded] = useState(false);
  const [partiesForDisplay, setPartiesForDisplay] = useState([]);
  const [underContractTransactions, setUnderContractTransactions] = useState(
    []
  );
  const [activeTransactions, setActiveTransactions] = useState([]);
  const [completedTransactions, setCompletedTransactions] = useState([]);
  const [archivedTransactions, setArchivedTransactions] = useState([]);
  const parties = partiesForUser?.parties;
  const column = partiesForUser?.column;
  const direction = partiesForUser?.direction;

  useEffect(() => {
    if (
      transactionsForUser?.length > 0 &&
      parties?.length > 0 &&
      currentUserProfile
    ) {
      let partiesWithTransaction = [];
      let underContract = [];
      let active = [];
      let completed = [];
      let archived = [];

      parties.forEach((party, index) => {
        const partyCopy = _.cloneDeep(party);
        const transaction = transactionsForUser.filter(
          (trans) => trans.id === party.transactionId
        )[0];
        if (transaction) {
          // Wait until after transaction to add party or extra parties appear
          partiesWithTransaction[index] = partyCopy;
          const transactionCopy = _.cloneDeep(transaction);

          // Add all existing fields
          partiesWithTransaction[index]["address"] =
            transactionCopy.address || {};
          partiesWithTransaction[index]["agentFirstName"] =
            transactionCopy.agentFirstName ||
            transactionCopy.agentProfile?.firstName ||
            "";
          partiesWithTransaction[index]["agentLastName"] =
            transactionCopy.agentLastName ||
            transactionCopy.agentProfile?.lastName ||
            "";
          partiesWithTransaction[index]["agentRepresents"] =
            transactionCopy.agentRepresents || "";
          partiesWithTransaction[index]["client"] =
            transactionCopy.client || {};
          partiesWithTransaction[index]["clientSecondary"] =
            transactionCopy.clientSecondary || {};
          partiesWithTransaction[index]["clientThird"] =
            transactionCopy.clientThird || {};
          partiesWithTransaction[index]["mlsNumbers"] =
            transactionCopy.mlsNumbers || [];
          partiesWithTransaction[index]["title"] = transactionCopy.title || "";
          partiesWithTransaction[index]["transactionPic"] =
            transactionCopy.pic || "";

          // Add new fields for the enhanced table
          partiesWithTransaction[index]["status"] =
            transactionCopy.status || "";
          partiesWithTransaction[index]["updatedAt"] =
            transactionCopy.updatedAt || null;
          partiesWithTransaction[index]["closingDateTime"] =
            transactionCopy.closingDateTime || null;

          // Check if user needs to sign documents
          const currentUserEmail = currentUserProfile?.email;
          const docsNeedingSigning =
            docsNeedingSignature?.filter(
              (doc) =>
                doc.transactionId === transaction.id &&
                doc.signingRequestedFor &&
                doc.signingRequestedFor.includes(currentUserEmail) &&
                (doc.status === "Awaiting Signature" ||
                  doc.status === "In Progress") &&
                !doc.signingComplete
            ) || [];

          partiesWithTransaction[index]["needsSignatures"] =
            docsNeedingSigning.length > 0;

          // Categorize by status
          const partyWithTrans = partiesWithTransaction[index];
          if (transactionCopy.status === "Under Contract") {
            underContract.push(partyWithTrans);
          } else if (
            transactionCopy.status === "Active Buyer" ||
            transactionCopy.status === "Active Listing"
          ) {
            active.push(partyWithTrans);
          } else if (transactionCopy.status === "Complete") {
            completed.push(partyWithTrans);
          } else if (transactionCopy.status === "Archived") {
            archived.push(partyWithTrans);
          }
        }
      });

      // Sort each category by default (Last Updated descending, except Completed which sorts by Closing Date descending)
      const sortByLastUpdated = (a, b) => {
        const dateA = a.updatedAt
          ? new Date(a.updatedAt.seconds * 1000)
          : new Date(0);
        const dateB = b.updatedAt
          ? new Date(b.updatedAt.seconds * 1000)
          : new Date(0);
        return dateB - dateA;
      };

      const sortByClosingDate = (a, b) => {
        const dateA = a.closingDateTime
          ? new Date(a.closingDateTime.seconds * 1000)
          : new Date(0);
        const dateB = b.closingDateTime
          ? new Date(b.closingDateTime.seconds * 1000)
          : new Date(0);
        return dateB - dateA;
      };

      setUnderContractTransactions(underContract.sort(sortByLastUpdated));
      setActiveTransactions(active.sort(sortByLastUpdated));
      setCompletedTransactions(completed.sort(sortByClosingDate));
      setArchivedTransactions(archived.sort(sortByLastUpdated));
      setPartiesForDisplay(partiesWithTransaction);
    }
    setLoaded(true);
  }, [parties, transactionsForUser, docsNeedingSignature, currentUserProfile]);

  if (!loaded) return <LoadingComponent content="Loading details..." />;

  function handleSort(column) {
    dispatch(sortPartiesForUser(column));
  }

  // Helper function to render a table section
  const renderTable = (transactions, title, showClosingDate = false) => {
    if (!transactions || transactions.length === 0) return null;

    return (
      <div className="medium bottom margin">
        <Header size="medium" color="blue" className="small bottom margin">
          {title}
        </Header>
        <Table compact sortable className="mini top margin">
          <Table.Header className="mobile hidden">
            <Table.Row className="small-header">
              <Table.HeaderCell></Table.HeaderCell>
              <Table.HeaderCell
                sorted={column === "address" ? direction : null}
                onClick={() => handleSort("address")}
              >
                Address
              </Table.HeaderCell>
              <Table.HeaderCell
                sorted={column === "agentFirstName" ? direction : null}
                onClick={() => handleSort("agentFirstName")}
              >
                Agent First Name
              </Table.HeaderCell>
              <Table.HeaderCell
                sorted={column === "agentLastName" ? direction : null}
                onClick={() => handleSort("agentLastName")}
              >
                Agent Last Name
              </Table.HeaderCell>
              <Table.HeaderCell
                sorted={column === "updatedAt" ? direction : null}
                onClick={() => handleSort("updatedAt")}
              >
                Last Updated
              </Table.HeaderCell>
              {showClosingDate && (
                <Table.HeaderCell
                  sorted={column === "closingDateTime" ? direction : null}
                  onClick={() => handleSort("closingDateTime")}
                >
                  Closing Date
                </Table.HeaderCell>
              )}
              <Table.HeaderCell
                sorted={column === "needsSignatures" ? direction : null}
                onClick={() => handleSort("needsSignatures")}
              >
                Needs Signatures
              </Table.HeaderCell>
              <Table.HeaderCell></Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {transactions.map((party) => (
              <PartyAllListItem
                party={party}
                key={party.id}
                showClosingDate={showClosingDate}
              />
            ))}
          </Table.Body>
        </Table>
      </div>
    );
  };

  return (
    <div className="main-page-wrapper">
      <>
        <Grid stackable className="small bottom margin">
          <Grid.Column computer={16} className="small bottom margin">
            <h3
              className="zero bottom margin"
              style={{ position: "absolute", bottom: "0" }}
            >
              Transaction List
            </h3>
          </Grid.Column>
          <Grid.Column computer={16} className="small bottom margin">
            {partiesForDisplay && partiesForDisplay.length !== 0 ? (
              <>
                {renderTable(underContractTransactions, "Under Contract", true)}
                {renderTable(activeTransactions, "Active Transactions")}
                {renderTable(completedTransactions, "Completed", true)}
                {renderTable(archivedTransactions, "Archived")}
              </>
            ) : (
              <Message size="large">
                If you do not see a transaction below, make sure you logged in
                using the same email where you received the notification that
                documents were shared with you.
                <br />
                Also, be sure you are not using a VPN to access this site.
                <br />
                You can access your account from your phone as an alternative
                way to log in and sign/view documents.
                <br />
                If you are still having problems, then reach out to the agent
                who sent you a notification.
              </Message>
            )}
          </Grid.Column>
        </Grid>
      </>
    </div>
  );
}
