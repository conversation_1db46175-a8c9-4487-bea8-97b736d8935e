import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Tab,
  <PERSON>,
  Button,
  Header,
  List,
  Input,
  Icon,
  Segment,
  Image,
} from "semantic-ui-react";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../app/common/modals/modalSlice";
import {
  updateVendorInDb,
} from "../../../app/firestore/firestoreService";
import { toast } from "react-toastify";

const defaultRoleTitles = [
  "Closer",
  "Assistant",
  "Sales Person",
  "Marketing Lead",
  "Manager",
  "Processor",
  "Coordinator",
  "Escrow Officer",
  "Title Examiner",
  "Settlement Agent",
];

export default function VendorCompanySettings({ vendorId }) {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);
  
  const [logoFile, setLogoFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState(currentUserProfile?.companyLogoUrl || "");
  const [roleTitles, setRoleTitles] = useState(
    currentUserProfile?.customRoleTitles || defaultRoleTitles
  );
  const [newRoleTitle, setNewRoleTitle] = useState("");
  const [uploading, setUploading] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setLogoPreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  const handleUploadLogo = async () => {
    if (!logoFile) return;
    
    setUploading(true);
    try {
      // TODO: Implement file upload to Firebase Storage
      // For now, we'll just simulate the upload
      console.log("Uploading logo file:", logoFile);
      
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const logoUrl = logoPreview; // In real implementation, this would be the uploaded file URL
      
      await updateVendorInDb(vendorId, {
        logoUrl: logoUrl,
      });
      
      toast.success("Company logo updated successfully!");
    } catch (error) {
      console.error("Error uploading logo:", error);
      toast.error("Error uploading logo. Please try again.");
    } finally {
      setUploading(false);
    }
  };

  const handleAddRoleTitle = () => {
    if (newRoleTitle.trim() && !roleTitles.includes(newRoleTitle.trim())) {
      setRoleTitles([...roleTitles, newRoleTitle.trim()]);
      setNewRoleTitle("");
    }
  };

  const handleRemoveRoleTitle = (titleToRemove) => {
    setRoleTitles(roleTitles.filter(title => title !== titleToRemove));
  };

  const handleSaveRoleTitles = async () => {
    setSaving(true);
    try {
      await updateVendorInDb(vendorId, {
        customRoleTitles: roleTitles,
      });
      
      toast.success("Role titles updated successfully!");
    } catch (error) {
      console.error("Error saving role titles:", error);
      toast.error("Error saving role titles. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const panes = [
    {
      menuItem: "Company Logo",
      render: () => (
        <Tab.Pane>
          <Header as="h4">Update Company Logo</Header>
          <Segment>
            {logoPreview && (
              <div style={{ marginBottom: "20px", textAlign: "center" }}>
                <Header as="h5">Current Logo</Header>
                <Image
                  src={logoPreview}
                  size="small"
                  centered
                  style={{ maxHeight: "150px", objectFit: "contain" }}
                />
              </div>
            )}
            
            <Form>
              <Form.Field>
                <label>Select New Logo</label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoChange}
                />
              </Form.Field>
              
              <Button
                color="blue"
                loading={uploading}
                disabled={!logoFile || uploading}
                onClick={handleUploadLogo}
                content="Upload Logo"
              />
            </Form>
          </Segment>
        </Tab.Pane>
      ),
    },
    {
      menuItem: "Role Titles",
      render: () => (
        <Tab.Pane>
          <Header as="h4">Manage Person Role Titles</Header>
          <Segment>
            <Form>
              <Form.Field>
                <label>Add New Role Title</label>
                <Input
                  placeholder="Enter role title"
                  value={newRoleTitle}
                  onChange={(e, { value }) => setNewRoleTitle(value)}
                  action={{
                    color: "green",
                    icon: "plus",
                    onClick: handleAddRoleTitle,
                    disabled: !newRoleTitle.trim(),
                  }}
                />
              </Form.Field>
            </Form>
            
            <Header as="h5" style={{ marginTop: "20px" }}>
              Current Role Titles
            </Header>
            <List>
              {roleTitles.map((title, index) => (
                <List.Item key={index}>
                  <List.Content floated="right">
                    <Button
                      size="mini"
                      color="red"
                      icon="trash"
                      onClick={() => handleRemoveRoleTitle(title)}
                    />
                  </List.Content>
                  <List.Content>
                    <List.Header>{title}</List.Header>
                  </List.Content>
                </List.Item>
              ))}
            </List>
            
            <Button
              color="blue"
              loading={saving}
              disabled={saving}
              onClick={handleSaveRoleTitles}
              content="Save Role Titles"
              style={{ marginTop: "20px" }}
            />
          </Segment>
        </Tab.Pane>
      ),
    },
  ];

  return (
    <ModalWrapper size="large" header="Company Settings">
      <div style={{ padding: "20px" }}>
        <Tab panes={panes} />
        
        <div style={{ textAlign: "center", marginTop: "30px" }}>
          <Button
            content="Close"
            onClick={() => dispatch(closeModal())}
          />
        </div>
      </div>
    </ModalWrapper>
  );
}
