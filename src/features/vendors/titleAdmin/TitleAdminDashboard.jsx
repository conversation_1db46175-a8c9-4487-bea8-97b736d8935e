import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Grid,
  Header,
  Segment,
  Button,
  List,
  Icon,
  Menu,
  Container,
  Divider,
} from "semantic-ui-react";
import { openModal } from "../../../app/common/modals/modalSlice";
import {
  fetchVendorOfficesFromDb,
  fetchVendorPeopleFromDb,
} from "../../../app/firestore/firestoreService";
import {
  fetchVendorOffices,
  fetchVendorPeople,
  setCurrentVendor,
} from "../vendorSlice";
import useFirestoreCollection from "../../../app/hooks/useFirestoreCollection";
import LoadingComponent from "../../../app/layout/LoadingComponent";

export default function TitleAdminDashboard() {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);
  const { vendorOffices, vendorPeople, currentVendor } = useSelector(
    (state) => state.vendor
  );
  const [selectedOffice, setSelectedOffice] = useState(null);
  const [activeMenuItem, setActiveMenuItem] = useState("offices");

  console.log("Title Admin Dash");
  console.log("currentUserProfile = ", currentUserProfile);
  console.log("currentVendor = ", currentVendor);

  // Set current vendor from user profile
  useEffect(() => {
    if (currentUserProfile?.vendorId?.length > 0) {
      dispatch(setCurrentVendor({ id: currentUserProfile.vendorId }));
    }
  }, [currentUserProfile, dispatch]);

  console.log("vendorId = ", currentUserProfile?.vendorId);

  // Fetch offices for the current vendor
  useFirestoreCollection({
    query: () =>
      currentVendor?.id?.length > 0
        ? fetchVendorOfficesFromDb(currentVendor.id)
        : null,
    data: (offices) => dispatch(fetchVendorOffices(offices)),
    deps: [dispatch, currentVendor?.id],
  });

  console.log("vendorOffices = ", vendorOffices);

  // Fetch people for the current vendor
  useFirestoreCollection({
    query: () =>
      currentVendor?.id ? fetchVendorPeopleFromDb(currentVendor.id) : null,
    data: (people) => dispatch(fetchVendorPeople(people)),
    deps: [dispatch, currentVendor?.id],
  });

  console.log("vendorPeople = ", vendorPeople);

  const handleOfficeSelect = (office) => {
    setSelectedOffice(office);
  };

  const handleAddOffice = () => {
    dispatch(
      openModal({
        modalType: "VendorOfficeForm",
        modalProps: { vendorId: currentVendor?.id },
      })
    );
  };

  const handleEditOffice = (office) => {
    dispatch(
      openModal({
        modalType: "VendorOfficeForm",
        modalProps: { office, vendorId: currentVendor?.id },
      })
    );
  };

  const handleAddPerson = () => {
    dispatch(
      openModal({
        modalType: "VendorPersonForm",
        modalProps: {
          vendorId: currentVendor?.id,
          officeId: selectedOffice?.id,
          offices: vendorOffices,
        },
      })
    );
  };

  const handleEditPerson = (person) => {
    dispatch(
      openModal({
        modalType: "VendorPersonForm",
        modalProps: {
          person,
          vendorId: currentVendor?.id,
          offices: vendorOffices,
        },
      })
    );
  };

  const handleCompanySettings = () => {
    dispatch(
      openModal({
        modalType: "VendorCompanySettings",
        modalProps: { vendorId: currentVendor?.id },
      })
    );
  };

  const getOfficepeople = (officeId) => {
    return vendorPeople.filter((person) => person.officeId === officeId);
  };

  const getSelectedOfficePeople = () => {
    if (!selectedOffice) return vendorPeople;
    return vendorPeople.filter(
      (person) => person.officeId === selectedOffice.id
    );
  };

  if (!currentUserProfile) {
    return <LoadingComponent />;
  }

  return (
    <div className="main-page-wrapper">
      <Container>
        <Header as="h1" textAlign="center">
          {currentUserProfile?.companyName || "Title Company"} Administration
        </Header>

        <Menu pointing secondary>
          <Menu.Item
            name="offices"
            active={activeMenuItem === "offices"}
            onClick={() => setActiveMenuItem("offices")}
          >
            <Icon name="building" />
            Offices & People
          </Menu.Item>
          <Menu.Item
            name="settings"
            active={activeMenuItem === "settings"}
            onClick={() => setActiveMenuItem("settings")}
          >
            <Icon name="settings" />
            Company Settings
          </Menu.Item>
        </Menu>

        {activeMenuItem === "offices" && (
          <Grid stackable>
            <Grid.Column width={6}>
              <Segment>
                <Header as="h3">
                  Offices
                  <Button
                    floated="right"
                    size="small"
                    color="blue"
                    icon="plus"
                    content="Add Office"
                    onClick={handleAddOffice}
                  />
                </Header>
                <Divider />
                {!vendorOffices || vendorOffices.length === 0 ? (
                  <p>No offices found. Add your first office to get started.</p>
                ) : (
                  <List selection>
                    {vendorOffices.map((office) => (
                      <List.Item
                        key={office.id}
                        active={selectedOffice?.id === office.id}
                        onClick={() => handleOfficeSelect(office)}
                      >
                        <List.Content>
                          <List.Header>{office.officeName}</List.Header>
                          <List.Description>
                            {office.address?.street}, {office.address?.city},{" "}
                            {office.address?.state}
                            <br />
                            Phone: {office.phone}
                            <br />
                            {getOfficepeople(office.id).length} people
                          </List.Description>
                          <Button
                            size="mini"
                            icon="edit"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditOffice(office);
                            }}
                            style={{ marginTop: "5px" }}
                          />
                        </List.Content>
                      </List.Item>
                    ))}
                  </List>
                )}
              </Segment>
            </Grid.Column>

            <Grid.Column width={10}>
              <Segment>
                <Header as="h3">
                  {selectedOffice
                    ? `People in ${selectedOffice.officeName}`
                    : "All People"}
                  <Button
                    floated="right"
                    size="small"
                    color="green"
                    icon="plus"
                    content="Add Person"
                    onClick={handleAddPerson}
                    disabled={!selectedOffice && vendorOffices.length === 0}
                  />
                </Header>
                <Divider />
                {getSelectedOfficePeople().length === 0 ? (
                  <p>
                    {selectedOffice
                      ? "No people in this office."
                      : "No people found."}
                  </p>
                ) : (
                  <List divided relaxed>
                    {getSelectedOfficePeople().map((person) => (
                      <List.Item key={person.id}>
                        <List.Content floated="right">
                          <Button
                            size="mini"
                            icon="edit"
                            onClick={() => handleEditPerson(person)}
                          />
                        </List.Content>
                        <List.Content>
                          <List.Header>
                            {person.firstName} {person.middleName}{" "}
                            {person.lastName}
                          </List.Header>
                          <List.Description>
                            <strong>Role:</strong> {person.roleTitle}
                            <br />
                            <strong>Phone:</strong> {person.phone}
                            <br />
                            <strong>Email:</strong> {person.email}
                            {person.assistantName && (
                              <>
                                <br />
                                <strong>Assistant:</strong>{" "}
                                {person.assistantName}
                              </>
                            )}
                          </List.Description>
                        </List.Content>
                      </List.Item>
                    ))}
                  </List>
                )}
              </Segment>
            </Grid.Column>
          </Grid>
        )}

        {activeMenuItem === "settings" && (
          <Segment>
            <Header as="h3">Company Settings</Header>
            <Button
              color="blue"
              icon="upload"
              content="Update Company Logo"
              onClick={handleCompanySettings}
            />
            <Button
              color="teal"
              icon="list"
              content="Manage Role Titles"
              onClick={handleCompanySettings}
              style={{ marginLeft: "10px" }}
            />
          </Segment>
        )}
      </Container>
    </div>
  );
}
