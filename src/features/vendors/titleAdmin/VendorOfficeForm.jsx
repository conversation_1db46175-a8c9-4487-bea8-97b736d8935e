import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useDispatch } from "react-redux";
import { Button } from "semantic-ui-react";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import MyTextInput from "../../../app/common/form/MyTextInput";
import { closeModal } from "../../../app/common/modals/modalSlice";
import {
  addVendorOfficeToDb,
  updateVendorOfficeInDb,
} from "../../../app/firestore/firestoreService";
import { toast } from "react-toastify";

export default function VendorOfficeForm({ office, vendorId }) {
  const dispatch = useDispatch();

  const initialValues = {
    officeName: office?.officeName || "",
    address: {
      street: office?.address?.street || "",
      unit: office?.address?.unit || "",
      city: office?.address?.city || "",
      state: office?.address?.state || "",
      zipcode: office?.address?.zipcode || "",
    },
    phone: office?.phone || "",
    fax: office?.fax || "",
    managerName: office?.managerName || "",
    managerPhone: office?.managerPhone || "",
    managerEmail: office?.managerEmail || "",
  };

  const validationSchema = Yup.object({
    officeName: Yup.string().required("Office name is required"),
    address: Yup.object({
      street: Yup.string().required("Street address is required"),
      city: Yup.string().required("City is required"),
      state: Yup.string().required("State is required"),
      zipcode: Yup.string().required("Zip code is required"),
    }),
    phone: Yup.string().required("Phone number is required"),
    managerEmail: Yup.string().email("Invalid email format"),
  });

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      if (office) {
        // Update existing office
        await updateVendorOfficeInDb(vendorId, office.id, values);
        toast.success("Office updated successfully");
      } else {
        // Add new office
        await addVendorOfficeToDb(vendorId, values);
        toast.success("Office added successfully");
      }
      dispatch(closeModal());
    } catch (error) {
      console.error("Error saving office:", error);
      toast.error("Error saving office. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <ModalWrapper
      size="large"
      header={office ? "Edit Office" : "Add New Office"}
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, isValid, dirty }) => (
          <Form className="ui form">
            <MyTextInput
              name="officeName"
              placeholder="Office Name"
              label="Office Name"
            />

            <h4>Address</h4>
            <MyTextInput
              name="address.street"
              placeholder="Street Address"
              label="Street Address"
            />
            <MyTextInput
              name="address.unit"
              placeholder="Unit/Suite (optional)"
              label="Unit/Suite"
            />
            <div style={{ display: "flex", gap: "10px" }}>
              <div style={{ flex: 2 }}>
                <MyTextInput
                  name="address.city"
                  placeholder="City"
                  label="City"
                />
              </div>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="address.state"
                  placeholder="State"
                  label="State"
                />
              </div>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="address.zipcode"
                  placeholder="Zip Code"
                  label="Zip Code"
                />
              </div>
            </div>

            <h4>Contact Information</h4>
            <div style={{ display: "flex", gap: "10px" }}>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="phone"
                  placeholder="Phone Number"
                  label="Phone Number"
                />
              </div>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="fax"
                  placeholder="Fax Number (optional)"
                  label="Fax Number"
                />
              </div>
            </div>

            <h4>Manager Information</h4>
            <MyTextInput
              name="managerName"
              placeholder="Manager Name (optional)"
              label="Manager Name"
            />
            <div style={{ display: "flex", gap: "10px" }}>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="managerPhone"
                  placeholder="Manager Phone (optional)"
                  label="Manager Phone"
                />
              </div>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="managerEmail"
                  placeholder="Manager Email (optional)"
                  label="Manager Email"
                />
              </div>
            </div>

            <div style={{ marginTop: "20px" }}>
              <Button
                type="submit"
                color="blue"
                loading={isSubmitting}
                disabled={!isValid || !dirty || isSubmitting}
                content={office ? "Update Office" : "Add Office"}
              />
              <Button
                type="button"
                content="Cancel"
                onClick={() => dispatch(closeModal())}
              />
            </div>
          </Form>
        )}
      </Formik>
    </ModalWrapper>
  );
}
