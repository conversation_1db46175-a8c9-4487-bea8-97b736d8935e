import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useDispatch } from "react-redux";
import { Button } from "semantic-ui-react";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import MyTextInput from "../../../app/common/form/MyTextInput";
import MySelectInput from "../../../app/common/form/MySelectInput";
import { closeModal } from "../../../app/common/modals/modalSlice";
import {
  addVendorPersonToDb,
  updateVendorPersonInDb,
} from "../../../app/firestore/firestoreService";
import { toast } from "react-toastify";

const defaultRoleTitles = [
  { key: "closer", value: "Closer", text: "Closer" },
  { key: "assistant", value: "Assistant", text: "Assistant" },
  { key: "sales", value: "Sales Person", text: "Sales Person" },
  { key: "marketing", value: "Marketing Lead", text: "Marketing Lead" },
  { key: "manager", value: "Manager", text: "Manager" },
  { key: "processor", value: "Processor", text: "Processor" },
  { key: "coordinator", value: "Coordinator", text: "Coordinator" },
];

export default function VendorPersonForm({ person, vendorId, officeId, offices }) {
  const dispatch = useDispatch();

  const officeOptions = offices.map((office) => ({
    key: office.id,
    value: office.id,
    text: office.officeName,
  }));

  const initialValues = {
    firstName: person?.firstName || "",
    middleName: person?.middleName || "",
    lastName: person?.lastName || "",
    roleTitle: person?.roleTitle || "",
    officeId: person?.officeId || officeId || "",
    phone: person?.phone || "",
    fax: person?.fax || "",
    email: person?.email || "",
    assistantName: person?.assistantName || "",
    assistantPhone: person?.assistantPhone || "",
    assistantEmail: person?.assistantEmail || "",
    secondAssistantName: person?.secondAssistantName || "",
    secondAssistantPhone: person?.secondAssistantPhone || "",
    secondAssistantEmail: person?.secondAssistantEmail || "",
  };

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First name is required"),
    lastName: Yup.string().required("Last name is required"),
    roleTitle: Yup.string().required("Role title is required"),
    officeId: Yup.string().required("Office is required"),
    phone: Yup.string().required("Phone number is required"),
    email: Yup.string().email("Invalid email format").required("Email is required"),
    assistantEmail: Yup.string().email("Invalid email format"),
    secondAssistantEmail: Yup.string().email("Invalid email format"),
  });

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      if (person) {
        // Update existing person
        await updateVendorPersonInDb(vendorId, person.id, values);
        toast.success("Person updated successfully");
      } else {
        // Add new person
        await addVendorPersonToDb(vendorId, values.officeId, values);
        toast.success("Person added successfully");
      }
      dispatch(closeModal());
    } catch (error) {
      console.error("Error saving person:", error);
      toast.error("Error saving person. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <ModalWrapper
      size="large"
      header={person ? "Edit Person" : "Add New Person"}
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, isValid, dirty }) => (
          <Form className="ui form">
            <h4>Personal Information</h4>
            <div style={{ display: "flex", gap: "10px" }}>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="firstName"
                  placeholder="First Name"
                  label="First Name"
                />
              </div>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="middleName"
                  placeholder="Middle Name (optional)"
                  label="Middle Name"
                />
              </div>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="lastName"
                  placeholder="Last Name"
                  label="Last Name"
                />
              </div>
            </div>

            <div style={{ display: "flex", gap: "10px" }}>
              <div style={{ flex: 1 }}>
                <MySelectInput
                  name="roleTitle"
                  placeholder="Select Role Title"
                  label="Role Title"
                  options={defaultRoleTitles}
                />
              </div>
              <div style={{ flex: 1 }}>
                <MySelectInput
                  name="officeId"
                  placeholder="Select Office"
                  label="Office"
                  options={officeOptions}
                />
              </div>
            </div>

            <h4>Contact Information</h4>
            <div style={{ display: "flex", gap: "10px" }}>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="phone"
                  placeholder="Phone Number"
                  label="Phone Number"
                />
              </div>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="fax"
                  placeholder="Fax Number (optional)"
                  label="Fax Number"
                />
              </div>
            </div>
            <MyTextInput
              name="email"
              placeholder="Email Address"
              label="Email Address"
            />

            <h4>Assistant Information (Optional)</h4>
            <MyTextInput
              name="assistantName"
              placeholder="Assistant Name"
              label="Assistant Name"
            />
            <div style={{ display: "flex", gap: "10px" }}>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="assistantPhone"
                  placeholder="Assistant Phone"
                  label="Assistant Phone"
                />
              </div>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="assistantEmail"
                  placeholder="Assistant Email"
                  label="Assistant Email"
                />
              </div>
            </div>

            <h4>Second Assistant Information (Optional)</h4>
            <MyTextInput
              name="secondAssistantName"
              placeholder="Second Assistant Name"
              label="Second Assistant Name"
            />
            <div style={{ display: "flex", gap: "10px" }}>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="secondAssistantPhone"
                  placeholder="Second Assistant Phone"
                  label="Second Assistant Phone"
                />
              </div>
              <div style={{ flex: 1 }}>
                <MyTextInput
                  name="secondAssistantEmail"
                  placeholder="Second Assistant Email"
                  label="Second Assistant Email"
                />
              </div>
            </div>

            <div style={{ marginTop: "20px" }}>
              <Button
                type="submit"
                color="blue"
                loading={isSubmitting}
                disabled={!isValid || !dirty || isSubmitting}
                content={person ? "Update Person" : "Add Person"}
              />
              <Button
                type="button"
                content="Cancel"
                onClick={() => dispatch(closeModal())}
              />
            </div>
          </Form>
        )}
      </Formik>
    </ModalWrapper>
  );
}
