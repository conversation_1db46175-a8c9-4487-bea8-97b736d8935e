import { createSlice } from "@reduxjs/toolkit";
import _ from "lodash";

const initialState = {
  vendors: [],
  titleCompanies: [],
  lenders: [],
  currentVendor: null,
  vendorOffices: [],
  vendorPeople: [],
  selectedTitleCompany: null,
  selectedOffice: null,
  selectedPerson: null,
  orderPreferences: {
    saveForFutureTransactions: false,
    addPersonToParties: false,
  },
  loading: false,
  error: null,
};

const vendorSlice = createSlice({
  name: "vendor",
  initialState,
  reducers: {
    setLoading(state, action) {
      state.loading = action.payload;
    },
    setError(state, action) {
      state.error = action.payload;
    },
    fetchVendors(state, action) {
      state.vendors = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchTitleCompanies(state, action) {
      state.titleCompanies = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchLenders(state, action) {
      state.lenders = action.payload;
      state.loading = false;
      state.error = null;
    },
    setCurrentVendor(state, action) {
      state.currentVendor = action.payload;
    },
    fetchVendorOffices(state, action) {
      state.vendorOffices = action.payload;
    },
    fetchVendorPeople(state, action) {
      state.vendorPeople = action.payload;
    },
    addVendorOffice(state, action) {
      state.vendorOffices.push(action.payload);
    },
    updateVendorOffice(state, action) {
      const index = state.vendorOffices.findIndex(
        (office) => office.id === action.payload.id
      );
      if (index !== -1) {
        state.vendorOffices[index] = action.payload;
      }
    },
    deleteVendorOffice(state, action) {
      state.vendorOffices = state.vendorOffices.filter(
        (office) => office.id !== action.payload
      );
      // Also remove people from deleted office
      state.vendorPeople = state.vendorPeople.filter(
        (person) => person.officeId !== action.payload
      );
    },
    addVendorPerson(state, action) {
      state.vendorPeople.push(action.payload);
    },
    updateVendorPerson(state, action) {
      const index = state.vendorPeople.findIndex(
        (person) => person.id === action.payload.id
      );
      if (index !== -1) {
        state.vendorPeople[index] = action.payload;
      }
    },
    deleteVendorPerson(state, action) {
      state.vendorPeople = state.vendorPeople.filter(
        (person) => person.id !== action.payload
      );
    },
    setSelectedTitleCompany(state, action) {
      state.selectedTitleCompany = action.payload;
      // Reset dependent selections
      state.selectedOffice = null;
      state.selectedPerson = null;
    },
    setSelectedOffice(state, action) {
      state.selectedOffice = action.payload;
      // Reset dependent selections
      state.selectedPerson = null;
    },
    setSelectedPerson(state, action) {
      state.selectedPerson = action.payload;
    },
    setOrderPreferences(state, action) {
      state.orderPreferences = {
        ...state.orderPreferences,
        ...action.payload,
      };
    },
    resetOrderSelections(state) {
      state.selectedTitleCompany = null;
      state.selectedOffice = null;
      state.selectedPerson = null;
      state.orderPreferences = {
        saveForFutureTransactions: false,
        addPersonToParties: false,
      };
    },
    clearError(state) {
      state.error = null;
    },
  },
});

export const {
  setLoading,
  setError,
  fetchVendors,
  fetchTitleCompanies,
  fetchLenders,
  setCurrentVendor,
  fetchVendorOffices,
  fetchVendorPeople,
  addVendorOffice,
  updateVendorOffice,
  deleteVendorOffice,
  addVendorPerson,
  updateVendorPerson,
  deleteVendorPerson,
  setSelectedTitleCompany,
  setSelectedOffice,
  setSelectedPerson,
  setOrderPreferences,
  resetOrderSelections,
  clearError,
} = vendorSlice.actions;

export default vendorSlice.reducer;
