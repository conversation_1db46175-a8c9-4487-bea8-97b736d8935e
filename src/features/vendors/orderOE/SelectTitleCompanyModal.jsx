import React, { useState } from "react";
import { useDispatch } from "react-redux";
import {
  Grid,
  Card,
  Image,
  Button,
  Header,
  Divider,
  Segment,
} from "semantic-ui-react";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../app/common/modals/modalSlice";
import { setSelectedTitleCompany } from "../vendorSlice";

export default function SelectTitleCompanyModal({ titleCompanies }) {
  const dispatch = useDispatch();
  const [showStandard, setShowStandard] = useState(false);

  const premierCompanies = titleCompanies.filter(
    (company) => company.subscriptionLevel === "premier"
  );
  const standardCompanies = titleCompanies.filter(
    (company) => company.subscriptionLevel === "standard"
  );

  const handleSelectCompany = (company) => {
    dispatch(setSelectedTitleCompany(company));
    dispatch(closeModal());
  };

  const CompanyCard = ({ company }) => (
    <Card
      onClick={() => handleSelectCompany(company)}
      style={{ cursor: "pointer", margin: "10px" }}
    >
      <Card.Content textAlign="center">
        {company.logoUrl ? (
          <Image
            src={company.logoUrl}
            size="small"
            centered
            style={{ maxHeight: "80px", objectFit: "contain" }}
          />
        ) : (
          <div
            style={{
              height: "80px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "#f8f8f8",
              border: "1px solid #ddd",
            }}
          >
            <span style={{ fontSize: "14px", color: "#666" }}>
              {company.companyName}
            </span>
          </div>
        )}
        <Card.Header style={{ marginTop: "10px", fontSize: "14px" }}>
          {company.companyName}
        </Card.Header>
      </Card.Content>
    </Card>
  );

  return (
    <ModalWrapper size="large" header="Select Title Company">
      <div style={{ padding: "20px" }}>
        <Header as="h4" textAlign="center" style={{ marginBottom: "20px" }}>
          The following title companies are integrated with TransActioner.
          <br />
          Select the company you wish to work with.
        </Header>

        {premierCompanies.length > 0 && (
          <>
            <Segment>
              <Header as="h3" color="blue" textAlign="center">
                Premier Partners
              </Header>
              <Grid stackable>
                {premierCompanies.map((company) => (
                  <Grid.Column
                    key={company.id}
                    width={4}
                    style={{ display: "flex", justifyContent: "center" }}
                  >
                    <CompanyCard company={company} />
                  </Grid.Column>
                ))}
              </Grid>
            </Segment>
            <Divider />
          </>
        )}

        {standardCompanies.length > 0 && (
          <>
            {!showStandard ? (
              <div style={{ textAlign: "center", margin: "20px 0" }}>
                <Button
                  basic
                  color="blue"
                  onClick={() => setShowStandard(true)}
                  content="Show More Title Companies..."
                />
              </div>
            ) : (
              <Segment>
                <Header as="h3" color="teal" textAlign="center">
                  Additional Partners
                </Header>
                <Grid stackable>
                  {standardCompanies.map((company) => (
                    <Grid.Column
                      key={company.id}
                      width={4}
                      style={{ display: "flex", justifyContent: "center" }}
                    >
                      <CompanyCard company={company} />
                    </Grid.Column>
                  ))}
                </Grid>
                <div style={{ textAlign: "center", marginTop: "20px" }}>
                  <Button
                    basic
                    color="grey"
                    onClick={() => setShowStandard(false)}
                    content="Show Less"
                  />
                </div>
              </Segment>
            )}
          </>
        )}

        {titleCompanies.length === 0 && (
          <Segment placeholder textAlign="center">
            <Header icon>
              <i className="building outline icon" />
              No Title Companies Available
            </Header>
            <p>
              There are currently no integrated title companies available.
              Please contact support for assistance.
            </p>
          </Segment>
        )}

        <div style={{ textAlign: "center", marginTop: "30px" }}>
          <Button
            content="Cancel"
            onClick={() => dispatch(closeModal())}
          />
        </div>
      </div>
    </ModalWrapper>
  );
}
