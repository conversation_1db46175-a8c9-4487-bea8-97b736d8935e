import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, Header, Segment, Container } from "semantic-ui-react";
import { openModal } from "../../../app/common/modals/modalSlice";
import {
  fetchVendorsByTypeFromDb,
  fetchVendorsBySubscriptionFromDb,
} from "../../../app/firestore/firestoreService";
import {
  fetchTitleCompanies,
  resetOrderSelections,
} from "../vendorSlice";
import useFirestoreCollection from "../../../app/hooks/useFirestoreCollection";

export default function OrderOEDashboard() {
  const dispatch = useDispatch();
  const { transaction } = useSelector((state) => state.transaction);
  const { titleCompanies, selectedTitleCompany, selectedOffice, selectedPerson } = useSelector(
    (state) => state.vendor
  );

  // Reset selections when component mounts
  useEffect(() => {
    dispatch(resetOrderSelections());
  }, [dispatch]);

  // Fetch title companies
  useFirestoreCollection({
    query: () => fetchVendorsByTypeFromDb("titleCompany"),
    data: (companies) => dispatch(fetchTitleCompanies(companies)),
    deps: [dispatch],
  });

  const handleSelectTitleCompany = () => {
    dispatch(
      openModal({
        modalType: "SelectTitleCompanyModal",
        modalProps: { titleCompanies },
      })
    );
  };

  const handleContinueOrder = () => {
    if (selectedTitleCompany && selectedOffice && selectedPerson) {
      dispatch(
        openModal({
          modalType: "OrderOEConfirmationModal",
          modalProps: {
            transaction,
            titleCompany: selectedTitleCompany,
            office: selectedOffice,
            person: selectedPerson,
          },
        })
      );
    }
  };

  return (
    <div className="secondary-page-wrapper">
      <Container>
        <Header as="h2" textAlign="center">
          Order Owner's & Encumbrance Report
        </Header>

        <Segment>
          <Header as="h3">Transaction Details</Header>
          <p>
            <strong>Property:</strong> {transaction?.address?.street},{" "}
            {transaction?.address?.city}, {transaction?.address?.state}
          </p>
          <p>
            <strong>Client:</strong> {transaction?.client?.firstName}{" "}
            {transaction?.client?.lastName}
          </p>
          <p>
            <strong>Status:</strong> {transaction?.status}
          </p>
        </Segment>

        <Segment>
          <Header as="h3">Order Process</Header>
          
          {!selectedTitleCompany && (
            <div>
              <p>Step 1: Select a Title Company</p>
              <Button
                color="blue"
                size="large"
                onClick={handleSelectTitleCompany}
                content="Select Title Company"
              />
            </div>
          )}

          {selectedTitleCompany && !selectedOffice && (
            <div>
              <p>✓ Title Company Selected: {selectedTitleCompany.companyName}</p>
              <p>Step 2: Select an Office</p>
              <Button
                color="blue"
                size="large"
                onClick={() =>
                  dispatch(
                    openModal({
                      modalType: "SelectOfficeModal",
                      modalProps: { titleCompany: selectedTitleCompany },
                    })
                  )
                }
                content="Select Office"
              />
            </div>
          )}

          {selectedTitleCompany && selectedOffice && !selectedPerson && (
            <div>
              <p>✓ Title Company Selected: {selectedTitleCompany.companyName}</p>
              <p>✓ Office Selected: {selectedOffice.officeName}</p>
              <p>Step 3: Select a Title Representative</p>
              <Button
                color="blue"
                size="large"
                onClick={() =>
                  dispatch(
                    openModal({
                      modalType: "SelectPersonModal",
                      modalProps: {
                        titleCompany: selectedTitleCompany,
                        office: selectedOffice,
                      },
                    })
                  )
                }
                content="Select Representative"
              />
            </div>
          )}

          {selectedTitleCompany && selectedOffice && selectedPerson && (
            <div>
              <p>✓ Title Company Selected: {selectedTitleCompany.companyName}</p>
              <p>✓ Office Selected: {selectedOffice.officeName}</p>
              <p>✓ Representative Selected: {selectedPerson.firstName} {selectedPerson.lastName}</p>
              <Button
                color="green"
                size="large"
                onClick={handleContinueOrder}
                content="Continue to Order Details"
              />
            </div>
          )}
        </Segment>
      </Container>
    </div>
  );
}
