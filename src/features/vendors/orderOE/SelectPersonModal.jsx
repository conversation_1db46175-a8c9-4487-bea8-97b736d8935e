import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Header,
  Segment,
  Checkbox,
  Form,
} from "semantic-ui-react";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../app/common/modals/modalSlice";
import { setSelectedPerson, setOrderPreferences, fetchVendorPeople } from "../vendorSlice";
import {
  fetchVendorPeopleByOfficeFromDb,
} from "../../../app/firestore/firestoreService";
import useFirestoreCollection from "../../../app/hooks/useFirestoreCollection";
import LoadingComponent from "../../../app/layout/LoadingComponent";

export default function SelectPersonModal({ titleCompany, office }) {
  const dispatch = useDispatch();
  const { vendorPeople, orderPreferences } = useSelector((state) => state.vendor);

  // Fetch people for the selected office
  useFirestoreCollection({
    query: () => fetchVendorPeopleByOfficeFromDb(titleCompany.id, office.id),
    data: (people) => dispatch(fetchVendorPeople(people)),
    deps: [dispatch, titleCompany.id, office.id],
  });

  const handleSelectPerson = (person) => {
    dispatch(setSelectedPerson(person));
    dispatch(closeModal());
  };

  const handlePreferenceChange = (field, value) => {
    dispatch(setOrderPreferences({ [field]: value }));
  };

  if (!vendorPeople) {
    return <LoadingComponent />;
  }

  return (
    <ModalWrapper size="large" header="Select Title Representative">
      <div style={{ padding: "20px" }}>
        <Header as="h4" textAlign="center" style={{ marginBottom: "20px" }}>
          Select a Title Closer or Sales Representative from {office.officeName}
        </Header>

        {vendorPeople.length === 0 ? (
          <Segment placeholder textAlign="center">
            <Header icon>
              <i className="user outline icon" />
              No Representatives Available
            </Header>
            <p>
              This office has no representatives configured.
              Please contact the title company for assistance.
            </p>
          </Segment>
        ) : (
          <>
            <List selection size="large">
              {vendorPeople.map((person) => (
                <List.Item
                  key={person.id}
                  onClick={() => handleSelectPerson(person)}
                  style={{ padding: "15px", cursor: "pointer" }}
                >
                  <List.Content>
                    <List.Header style={{ fontSize: "16px", marginBottom: "5px" }}>
                      {person.firstName} {person.middleName} {person.lastName}
                    </List.Header>
                    <List.Description>
                      <div style={{ marginBottom: "5px" }}>
                        <strong>Role:</strong> {person.roleTitle}
                      </div>
                      <div style={{ marginBottom: "5px" }}>
                        <strong>Phone:</strong> {person.phone}
                        {person.fax && (
                          <>
                            <br />
                            <strong>Fax:</strong> {person.fax}
                          </>
                        )}
                      </div>
                      <div style={{ marginBottom: "5px" }}>
                        <strong>Email:</strong> {person.email}
                      </div>
                      {person.assistantName && (
                        <div style={{ marginBottom: "5px" }}>
                          <strong>Assistant:</strong> {person.assistantName}
                          {person.assistantPhone && ` - ${person.assistantPhone}`}
                          {person.assistantEmail && ` (${person.assistantEmail})`}
                        </div>
                      )}
                      {person.secondAssistantName && (
                        <div>
                          <strong>Second Assistant:</strong> {person.secondAssistantName}
                          {person.secondAssistantPhone && ` - ${person.secondAssistantPhone}`}
                          {person.secondAssistantEmail && ` (${person.secondAssistantEmail})`}
                        </div>
                      )}
                    </List.Description>
                  </List.Content>
                </List.Item>
              ))}
            </List>

            <Segment style={{ marginTop: "20px" }}>
              <Header as="h4">Preferences</Header>
              <Form>
                <Form.Field>
                  <Checkbox
                    label="Save these selections for all future Seller transactions"
                    checked={orderPreferences.saveForFutureTransactions}
                    onChange={(e, { checked }) =>
                      handlePreferenceChange("saveForFutureTransactions", checked)
                    }
                  />
                </Form.Field>
                <Form.Field>
                  <Checkbox
                    label="Add this Title person to Parties for this transaction"
                    checked={orderPreferences.addPersonToParties}
                    onChange={(e, { checked }) =>
                      handlePreferenceChange("addPersonToParties", checked)
                    }
                  />
                </Form.Field>
              </Form>
            </Segment>
          </>
        )}

        <div style={{ textAlign: "center", marginTop: "30px" }}>
          <Button
            content="Back"
            onClick={() => dispatch(closeModal())}
          />
        </div>
      </div>
    </ModalWrapper>
  );
}
