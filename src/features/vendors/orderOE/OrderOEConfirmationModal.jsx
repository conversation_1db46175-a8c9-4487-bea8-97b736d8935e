import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON>rid,
  Header,
  Segment,
  Button,
  Form,
  TextArea,
} from "semantic-ui-react";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../app/common/modals/modalSlice";
import { toast } from "react-toastify";

export default function OrderOEConfirmationModal({
  transaction,
  titleCompany,
  office,
  person,
}) {
  const dispatch = useDispatch();
  const { currentUserProfile } = useSelector((state) => state.profile);
  const [message, setMessage] = useState("");
  const [sending, setSending] = useState(false);

  const handleSendOrder = async () => {
    setSending(true);
    try {
      // Here you would implement the email sending functionality
      // For now, we'll just show a success message
      
      // Prepare email data
      const emailData = {
        to: person.email,
        cc: [
          currentUserProfile.email,
          ...(currentUserProfile.assistantEmail ? [currentUserProfile.assistantEmail] : [])
        ],
        subject: `O&E Order Request - ${transaction.address?.street}`,
        agentInfo: {
          name: `${currentUserProfile.firstName} ${currentUserProfile.lastName}`,
          phone: currentUserProfile.phone,
          email: currentUserProfile.email,
          brokerageName: currentUserProfile.brokerageName,
          brokerageAddress: currentUserProfile.address,
        },
        titleCompanyInfo: {
          companyName: titleCompany.companyName,
          officeName: office.officeName,
          address: office.address,
          personName: `${person.firstName} ${person.lastName}`,
          personRole: person.roleTitle,
          personPhone: person.phone,
          personEmail: person.email,
        },
        transactionInfo: {
          sellerNames: `${transaction.client?.firstName} ${transaction.client?.lastName}`,
          address: transaction.address,
          legalDescription: transaction.legalDescription,
          county: transaction.county,
        },
        message: message,
      };

      // TODO: Implement actual email sending service
      console.log("Email data to send:", emailData);
      
      toast.success("O&E order request sent successfully!");
      dispatch(closeModal());
    } catch (error) {
      console.error("Error sending order:", error);
      toast.error("Error sending order. Please try again.");
    } finally {
      setSending(false);
    }
  };

  return (
    <ModalWrapper size="large" header="Confirm O&E Order">
      <div style={{ padding: "20px" }}>
        <Grid stackable>
          <Grid.Column width={8}>
            <Segment>
              <Header as="h4">Your Information</Header>
              <p>
                <strong>Name:</strong> {currentUserProfile.firstName}{" "}
                {currentUserProfile.lastName}
              </p>
              <p>
                <strong>Phone:</strong> {currentUserProfile.phone}
              </p>
              <p>
                <strong>Email:</strong> {currentUserProfile.email}
              </p>
              <p>
                <strong>Brokerage:</strong> {currentUserProfile.brokerageName}
              </p>
              {currentUserProfile.address && (
                <p>
                  <strong>Brokerage Address:</strong>{" "}
                  {currentUserProfile.address.street},{" "}
                  {currentUserProfile.address.city},{" "}
                  {currentUserProfile.address.state}{" "}
                  {currentUserProfile.address.zipcode}
                </p>
              )}
            </Segment>

            <Segment>
              <Header as="h4">Title Company Information</Header>
              <p>
                <strong>Company:</strong> {titleCompany.companyName}
              </p>
              <p>
                <strong>Office:</strong> {office.officeName}
              </p>
              <p>
                <strong>Address:</strong> {office.address?.street}
                {office.address?.unit && `, ${office.address.unit}`}
                <br />
                {office.address?.city}, {office.address?.state}{" "}
                {office.address?.zipcode}
              </p>
              <p>
                <strong>Contact:</strong> {person.firstName} {person.lastName} ({person.roleTitle})
              </p>
              <p>
                <strong>Phone:</strong> {person.phone}
              </p>
              <p>
                <strong>Email:</strong> {person.email}
              </p>
            </Segment>
          </Grid.Column>

          <Grid.Column width={8}>
            <Segment>
              <Header as="h4">Transaction Information</Header>
              <p>
                <strong>Seller(s):</strong> {transaction.client?.firstName}{" "}
                {transaction.client?.lastName}
                {transaction.clientSecondaryExists && (
                  <>
                    , {transaction.clientSecondary?.firstName}{" "}
                    {transaction.clientSecondary?.lastName}
                  </>
                )}
                {transaction.clientThirdExists && (
                  <>
                    , {transaction.clientThird?.firstName}{" "}
                    {transaction.clientThird?.lastName}
                  </>
                )}
              </p>
              <p>
                <strong>Property Address:</strong> {transaction.address?.street}
                {transaction.address?.unit && `, ${transaction.address.unit}`}
                <br />
                {transaction.address?.city}, {transaction.address?.state}{" "}
                {transaction.address?.zipcode}
              </p>
              {transaction.legalDescription && (
                <p>
                  <strong>Legal Description:</strong> {transaction.legalDescription}
                </p>
              )}
              {transaction.county && (
                <p>
                  <strong>County:</strong> {transaction.county}
                </p>
              )}
            </Segment>

            <Segment>
              <Header as="h4">Additional Message (Optional)</Header>
              <Form>
                <TextArea
                  placeholder="Enter any additional information or special requests..."
                  value={message}
                  onChange={(e, { value }) => setMessage(value)}
                  rows={4}
                />
              </Form>
            </Segment>
          </Grid.Column>
        </Grid>

        <div style={{ textAlign: "center", marginTop: "30px" }}>
          <Button
            color="green"
            size="large"
            loading={sending}
            disabled={sending}
            onClick={handleSendOrder}
            content="Send O&E Order Request"
          />
          <Button
            content="Cancel"
            onClick={() => dispatch(closeModal())}
            style={{ marginLeft: "10px" }}
          />
        </div>
      </div>
    </ModalWrapper>
  );
}
