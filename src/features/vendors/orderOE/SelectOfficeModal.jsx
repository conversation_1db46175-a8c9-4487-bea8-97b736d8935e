import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Header,
  Segment,
} from "semantic-ui-react";
import ModalWrapper from "../../../app/common/modals/modalWrapper";
import { closeModal } from "../../../app/common/modals/modalSlice";
import { setSelectedOffice, fetchVendorOffices } from "../vendorSlice";
import {
  fetchVendorOfficesFromDb,
} from "../../../app/firestore/firestoreService";
import useFirestoreCollection from "../../../app/hooks/useFirestoreCollection";
import LoadingComponent from "../../../app/layout/LoadingComponent";

export default function SelectOfficeModal({ titleCompany }) {
  const dispatch = useDispatch();
  const { vendorOffices } = useSelector((state) => state.vendor);

  // Fetch offices for the selected title company
  useFirestoreCollection({
    query: () => fetchVendorOfficesFromDb(titleCompany.id),
    data: (offices) => dispatch(fetchVendorOffices(offices)),
    deps: [dispatch, titleCompany.id],
  });

  const handleSelectOffice = (office) => {
    dispatch(setSelectedOffice(office));
    dispatch(closeModal());
  };

  if (!vendorOffices) {
    return <LoadingComponent />;
  }

  return (
    <ModalWrapper size="large" header="Select Office">
      <div style={{ padding: "20px" }}>
        <Header as="h4" textAlign="center" style={{ marginBottom: "20px" }}>
          Select an office from {titleCompany.companyName}
        </Header>

        {vendorOffices.length === 0 ? (
          <Segment placeholder textAlign="center">
            <Header icon>
              <i className="building outline icon" />
              No Offices Available
            </Header>
            <p>
              This title company has no offices configured.
              Please contact the title company for assistance.
            </p>
          </Segment>
        ) : (
          <List selection size="large">
            {vendorOffices.map((office) => (
              <List.Item
                key={office.id}
                onClick={() => handleSelectOffice(office)}
                style={{ padding: "15px", cursor: "pointer" }}
              >
                <List.Content>
                  <List.Header style={{ fontSize: "16px", marginBottom: "5px" }}>
                    {office.officeName}
                  </List.Header>
                  <List.Description>
                    <div style={{ marginBottom: "5px" }}>
                      <strong>Address:</strong> {office.address?.street}
                      {office.address?.unit && `, ${office.address.unit}`}
                      <br />
                      {office.address?.city}, {office.address?.state} {office.address?.zipcode}
                    </div>
                    <div style={{ marginBottom: "5px" }}>
                      <strong>Phone:</strong> {office.phone}
                      {office.fax && (
                        <>
                          <br />
                          <strong>Fax:</strong> {office.fax}
                        </>
                      )}
                    </div>
                    {office.managerName && (
                      <div>
                        <strong>Manager:</strong> {office.managerName}
                        {office.managerPhone && ` - ${office.managerPhone}`}
                      </div>
                    )}
                  </List.Description>
                </List.Content>
              </List.Item>
            ))}
          </List>
        )}

        <div style={{ textAlign: "center", marginTop: "30px" }}>
          <Button
            content="Back"
            onClick={() => dispatch(closeModal())}
          />
        </div>
      </div>
    </ModalWrapper>
  );
}
